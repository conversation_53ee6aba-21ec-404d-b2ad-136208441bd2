import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:eam/be/PM_OPTIONS_HEADER.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/presentation/common_widgets/connectivity_card.dart';
import 'package:eam/presentation/eam_packages/web_menu/menu_controller.dart';
import 'package:eam/presentation/common_widgets/eam_common_widget.dart';
import 'package:eam/presentation/common_widgets/eam_icon_button.dart';
import 'package:eam/presentation/common_widgets/searchbar_new.dart';
import 'package:eam/presentation/pages/dashboard/mobile/mobile_dashboard.dart';
import 'package:eam/presentation/pages/dashboard/widgets/user_workingtime_txt.dart';
import 'package:eam/presentation/pages/manager/dashboard/manager_dashboard.dart';
import 'package:eam/presentation/pages/work_orders/widgets/order_list.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/presentation/widgets/progress_bar_eam.dart';
import 'package:eam/provider/chart/barchart_provider.dart';
import 'package:eam/provider/chart/piechart_provider.dart';
import 'package:eam/provider/assistant/FabVisibilityProvider.dart';
import 'package:eam/provider/progress_provider.dart';
import 'package:eam/provider/server_connection_provider.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:eam/utils/app_notifier.dart';
import 'package:eam/utils/utilshared.dart';
import 'package:eam/widgets/common_widget.dart';
import 'package:eam/widgets/eam_map/eam_arcgis_map_widget.dart';
import 'package:eam/widgets/eam_map/eam_google_map_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/main.dart';

import '../../../../../helpers/application_helper.dart';
import '../../../../../helpers/pa_helper.dart';
import '../../../../../helpers/ui_helper.dart';
import '../../../../../models/menu_action_dropdown.dart';
import '../../../../../utils/constants.dart';
import '../../../../../widgets/menu_action_button.dart';
import '../../../../common_widgets/inkwell.dart';
import '../../widgets/summary_section.dart';

class WebHomeView extends StatefulWidget {
  WebHomeView({Key? key, required this.removeItems}) : super(key: key);
  final List<String> removeItems;
  @override
  State<WebHomeView> createState() => _WebHomeViewState();
}

class _WebHomeViewState extends State<WebHomeView> {
  AppNotifier _appNotifier = AppNotifier();
  bool timerRunning = false;
  
  var isDeleting = false;
  void initProvider() async {
    context.read<OrderNotifier>().getOrders(context, from: 'WebHomeView');
    context.read<ProgressProvider>().setInboxCount();
    context.read<PieChartProvider>().loadPieChartData(context: context);
    context.read<BarChartProvider>().loadBarChartData(context: context);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateFabVisibility("Dashboard");
  }

  void _updateFabVisibility(String? contextMessage) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FabVisibilityProvider>(context, listen: false)
          .updateContext(contextMessage);
    });
  }

  @override
  void initState() {
    _appNotifier.notifySyncStatus(appNotifierCallback);
    _appNotifier.notifyInfoMessages(appInfoMessageCallback);
    Future.microtask(() => context.read<OrderNotifier>().resetFetchingOrder());
    initProvider();
    super.initState();
  }

  @override
  void dispose() {
    if (mounted) {
      _appNotifier.unSubscribeNotifySyncStatus();
      _appNotifier.unSubscribeInfoMessage();
    }
    // _updateFabVisibility("");
    super.dispose();
  }

  appNotifierCallback(data) {
    if (mounted) {
      context.read<ProgressProvider>().addInboxCount(data);
    }
    if (data[EventSyncStatusFieldType] ==
        EventSyncStatusTypeInboxProcessingComplete) {
      if (mounted) {
        initProvider();
      }
    }
    if (mounted) {
      if ((data[EventSyncStatusFieldType] == EventSyncStatusTypeInbox &&
          (data[EventSyncStatusFieldInboxCount] == 0 &&
              data[EventSyncStatusFieldSentItemsCount] == 0))) {
        initProvider();
      } else if (data[EventSyncStatusFieldType] == EventSyncStatusTypeSent) {
        initProvider();
      } else if (data[EventSyncStatusFieldType] == EventSyncStatusTypeDeleted) {
      } else {}
    }

    // if (mounted) {
    //   context.read<ProgressProvider>().setInboxCount();
    // }
  }

  appInfoMessageCallback(data) {
    print("appInfoMessageCallback 2:" + data.toString());
    List<dynamic> dataList = data['data'];
    if (mounted) {
      if (dataList
          .where((element) =>
              element['category'] == "FAILURE" ||
              element['category'] == "FAILURE_N_PROCESS")
          .isNotEmpty) {
        print("FAILED ========>");
        initProvider();
      }

      // if (mounted) {
      //   context.read<ProgressProvider>().setInboxCount();
      // }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PlatformDetails.isMobileScreen(context)
        ? MobileHomeView(
            removeItems: widget.removeItems,
          )
        : Scaffold(body: _bodyWidgets());
  }

  _bodyWidgets() {
    return Padding(
      padding: Dimensions.padding(context),
      child: Container(
        child: Column(
          children: [
            header(),
            // SizedBox(height: 10),
            // if (PlatformDetails.isTabPortraitScreen(context)) ...[
            //   Padding(
            //     padding: EdgeInsets.only(
            //         left: TabPortraitOrMobile.isTabPortraitOrMobile(context)
            //             ? Dimensions.mobilePadding
            //             : Dimensions.webPadding),
            //     child: _getSummarySection(),
            //   ),
            // ],
            Padding(
              padding: Dimensions.padding(context),
              child: ProgressIndicatorEam(),
            ),
            SizedBox(height: 16),
            Expanded(child: DashboardScreen()),
            // Padding(
            //   padding: Dimensions.padding(context),
            //   child: _ordersViewAllRow(),
            // ),
            // SizedBox(height: 12),
            // _orderList(),
            // SizedBox(height: 20),
            // if (!PlatformDetails.isMobileScreen(context) &&
            //     !PlatformDetails.isTabPortraitScreen(context)) ...[
            //   Padding(
            //     padding: Dimensions.padding(context),
            //     child: _getSummarySection(),
            //   ),
            // ],
            // SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // _orderList() {
  //   return Consumer<OrderNotifier>(builder: (context, pro, child) {
  //     var size = MediaQuery.of(context).size;
  //     if (pro.fetchingOrders) {
  //       return SizedBox(
  //           height: MediaQuery.of(context).size.height / 2,
  //           child: Center(child: CircularProgressIndicator()));
  //     }
  //     if (pro.orders.length == 0) {
  //       return Expanded(
  //         child: Center(
  //           child: Container(
  //             height: size.height / 2,
  //             width: size.width / 2,
  //             child: EamNoRecordWidget(),
  //           ),
  //         ),
  //       );
  //     }
  //     return Expanded(
  //       child: Padding(
  //         padding: Dimensions.padding(context),
  //         child: OrderList(
  //           isAllVisible: false,
  //           genericList: pro.orders,
  //           isHistoryPage: false,
  //         ),
  //       ),
  //     );
  //   });
  // }

  // _getSummarySection() {
  //   if (PlatformDetails.isTabPortraitScreen(context))
  //     return SizedBox(
  //       height: 123,
  //       child: Row(
  //         children: [
  //           SummarySectionWidget(
  //             removeItems: widget.removeItems,
  //           )
  //         ],
  //       ),
  //     );

  //   return SummarySectionWidget(
  //     removeItems: widget.removeItems,
  //   );
  // }

  header() {
    return Column(
      children: [
        SizedBox(height: 30),
        // if (PlatformDetails.isTabPortraitScreen(context)) ...[
        //   Row(
        //     children: [
        //       // Expanded(
        //       //   child: _getSearchBar(true),
        //       // ),
        //       25.0.spaceX,
        //       _getActionButton(true),
        //     ],
        //   ),
        //   SizedBox(
        //     height: 16,
        //   ),
        // ],
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _getGreetings(),
            Row(
              children: [
                ApplicationHelper.isMabEnabled()
                    ? EamIconButton(
                        title: AppLocalizations.of(context)!.mapsString,
                        onTap: () async {
                          await navigateToMaps(context);
                        },
                        icon: EamIcon(iconName: EamIcon.map))
                    : SizedBox(),
                // if (!PlatformDetails.isMobileScreen(context) &&
                //     !PlatformDetails.isTabPortraitScreen(context)) ...[
                SizedBox(width: 12),
                Row(
                  children: [
                    // _getSearchBar(false),
                    // 20.0.spaceX,
                    _getActionButton(true)
                  ],
                )
                // ],
              ],
            ),
          ],
        ),
      ],
    );
  }

  Future navigateToMaps(BuildContext context) async {
    try {
      PM_OPTIONS_HEADER optionsHeader =
          await DbHelper.getOptionsHeader(Constants.MAP);

      Map<String, dynamic> map = json.decode(optionsHeader.field_value!);

      if (map['type'] != null) {
        switch (map['type']) {
          case 'esri':
            NavigationService.pushNamed(EamArcgisMapWidget.routeName,
                arguments: EamMapWidgetArguments());
            break;
          case 'google':
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => EamGoogleMapWidget(
                        arguments: EamMapWidgetArguments(
                            androidApiKey: ApplicationHelper.getHomeContent()
                                    ?.data["androidApiKey"] ??
                                '',
                            directionApiKey: ApplicationHelper.getHomeContent()
                                    ?.data["directionApiKey"] ??
                                '',
                            iosApiKey: ApplicationHelper.getHomeContent()
                                    ?.data["iosApiKey"] ??
                                '',
                            javascriptApiKey: ApplicationHelper.getHomeContent()
                                    ?.data["javascriptApiKey"] ??
                                ' '),
                      )),
            );
            break;
          default:
            break;
        }
      }
    } catch (e) {
      UIHelper.showSnackBar(context, message: '$e');
    }
  }

  _getGreetings() {
    return PlatformDetails.isTabPortraitScreen(context)
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                greeting(),
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
              ),
              SizedBox(
                height: 3,
              ),
              Row(
                children: [
                  EamCommonWidget.getUsername(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [WorkingHourText()],
                  )
                ],
              )
            ],
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    greeting(),
                    style: TextStyle(fontSize: 20),
                  ),
                  EamCommonWidget.getUsername(),
                ],
              ),
              WorkingHourText()
            ],
          );
  }

  // _getSearchBar(isTab) {
  //   return EamSearchBar2(
  //     isTab: isTab,
  //     onScanned: _onScanned,
  //     onSearchValueChange: _onSearchValueChange,
  //     isRightPaddingRequired: true,
  //   );
  // }

  _getActionButton(bool showBorder) {
    return MenuActionButton(
        showborder: showBorder,
        options: [
          MenuActionItem(
            value: Constants.INITIALIZATION,
            name: AppLocalizations.of(context)!.updateCustomization,
          ),
          if (!kIsWeb) ...[
            MenuActionItem(
              value: Constants.SETTINGS,
              name: AppLocalizations.of(context)!.settingsString,
            ),
            MenuActionItem(
              value: 'refresh',
              name: AppLocalizations.of(context)!.refreshString,
            ),
          ],
          if (kIsWeb) ...[
            MenuActionItem(
              value: Constants.APPLICATION_VERSION,
              name: AppLocalizations.of(context)!.version,
            ),
          ],
          MenuActionItem(
            value: Constants.LOG_OUT,
            name: AppLocalizations.of(context)!.logout,
          ),
        ],
        onOptionItemSelected: (item) async {
          switch (item.value) {
            case Constants.INITIALIZATION:
              await getCustomizationInSync();
              break;
            case Constants.SETTINGS:
              _navigateToSettingsPage();
              break;
            case Constants.LOG_OUT:
              _confirmAndLogout();
              break;
            case Constants.APPLICATION_VERSION:
              _showVersion();
              break;
            case 'refresh':
              await SyncEngine().receive();
              break;
          }
        });
  }

  Future<void> getCustomizationInSync() async {
    try {
      UIHelper.showEamProgressDialog(
        context,
        title: AppLocalizations.of(context)!
            .pleaseWaitWhileDownloadingCustomization,
      );
      Result result = await PAHelper.getCustomizationInSyncMode();
      await ApplicationHelper.downloadHomePageConfigData();
      Navigator.of(context, rootNavigator: true).pop();
      if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        UIHelper.showEamDialog(
          context,
          dismissible: false,
          title: AppLocalizations.of(context)!.masterDataUpdated,
          description: kIsWeb
              ? AppLocalizations.of(context)!.reloadApp
              : AppLocalizations.of(context)!.restartApp,
        );
      } else {
        UIHelper.showSnackBar(context, message: "Error");
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      Logger.logError('WebHomeView', 'getCustomizationInSync', e.toString());
      UIHelper.showSnackBar(context, message: e.toString());
    }
    return;
  }

  _navigateToSettingsPage() {
    // NavigationService.navigateToIndependentScreen("/unviredSettings",
    //         arguments: ThemeData(useMaterial3: false))
    //     .then((value) {
    //   context.read<ProgressProvider>().setInboxCount();
    // });

    Navigator.push(
        context,
        CupertinoPageRoute(
            builder: ((context) => Settings(
                  themeData: ThemeData(useMaterial3: false),
                )))).then((value) {
      context.read<ProgressProvider>().setInboxCount();
    });
  }

  // _onScanned(value) {
  //   context
  //       .read<OrderNotifier>()
  //       .filterOrderList(searchString: value.toLowerCase());
  // }

  // _onSearchValueChange(value) {
  //   context
  //       .read<OrderNotifier>()
  //       .filterOrderList(searchString: value.toLowerCase());
  // }

  // _ordersViewAllRow() {
  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       Text(
  //         AppLocalizations.of(context)!.your_orders,
  //         style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
  //       ),
  //       Consumer<OrderNotifier>(builder: (context, ref, child) {
  //         return ref.orders.isEmpty
  //             ? SizedBox()
  //             : InkWellCard(
  //                 color: Colors.transparent,
  //                 borderRadius: 0,
  //                 onTap: () async {
  //                   context.read<OrderNotifier>().clearSearchQuery();
  //                   final _index = context
  //                       .read<MenuControllerNotifier>()
  //                       .getIndex(Constants.ORDERS);
  //                   if (_index != -1) {
  //                     Provider.of<MenuControllerNotifier>(context,
  //                             listen: false)
  //                         .changeIndex(_index);
  //                     NavigationService.addCurrentIndex(_index);
  //                   }
  //                 },
  //                 child: Text(
  //                   AppLocalizations.of(context)!.view_all,
  //                   style: TextStyle(
  //                     fontSize: 14,
  //                     fontWeight: FontWeight.w500,
  //                     decoration: TextDecoration.underline,
  //                     color: Theme.of(context).primaryColor,
  //                   ),
  //                 ),
  //               );
  //       })
  //     ],
  //   );
  // }

  _confirmAndLogout() {
    if (!kIsWeb) {
      _clearData();
    } else {
      UIHelper.showEamDialog2(
        context,
        positiveActionLabel: AppLocalizations.of(context)!.okayString,
        negativeActionLabel: AppLocalizations.of(context)!.cancel,
        title: AppLocalizations.of(context)!.confirmation,
        description: AppLocalizations.of(context)!.logoutConfirmation,
        onPositiveClickListener: () async {
          Future.delayed(Duration(seconds: 2), () async {
            await SettingsHelper().clearData();
          });
          UIHelper.closeDialog(context);
          String str2 =
              '''Deleted all the data for this application from device.''';
          noButtonShowAlertDialog(str2, context.locale.alertString);
          Future.delayed(Duration(seconds: 3), () async {
            WindowUtil.reloadWindow();
          });
        },
        onNegativeClickListener: () => UIHelper.closeDialog(context),
      );
    }
  }

  void _clearData() {
    String str =
        '''This option will clear all the data associated with the application and restore the application to freshly installed state (requires application restart). Deleted data cannot be recovered. Are you sure you want to clear the data in the application ? ''';

    showAlertDialog(str, context.locale.logout);
  }

  showAlertDialog(String text, String title) {
    Timer(Duration(seconds: 3), () {});
    // show the dialog
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(text),
          actions: [
            TextButton(
              child: Text(context.locale.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                context.locale.logout,
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () async {
                String str2 =
                    '''Deleted all the data for this application from device. Please close the application.''';
                Navigator.of(context).pop();
                noButtonShowAlertDialog(str2, context.locale.alertString);
                setState(() {
                  isDeleting = true;
                });
                await Future.delayed(Duration(seconds: 2), () async {
                  await SettingsHelper().clearData();
                });
                setState(() {
                  isDeleting = false;
                });
              },
            ),
          ],
        );
      },
    );
  }

  noButtonShowAlertDialog(String text, String title) {
    Timer(Duration(seconds: 3), () {});
    // show the dialog
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () => Future.value(false),
          child: AlertDialog(
            title: Text(title),
            content: isDeleting ? Row(
              children: [
                CircularProgressIndicator(),
              ],
            ) : Text(text),
          ),
        );
      },
    );
  }

  _showVersion() async {
    String version =
        "Application Version: " + (await PackageInfo.fromPlatform()).version;
    version += "\nFw Version: " + SettingsHelper().getFrameworkVersionNumber();
    version +=
        "\nBuild Number: " + (await PackageInfo.fromPlatform()).buildNumber;

    UIHelper.showEamDialog2(
      context,
      positiveActionLabel: AppLocalizations.of(context)!.okayString,
      // negativeActionLabel: AppLocalizations.of(context)!.cancel,
      title: AppLocalizations.of(context)!.infoString,
      description: version,
      onPositiveClickListener: () async {
        UIHelper.closeDialog(context);
      },
      // onNegativeClickListener: () => UIHelper.closeDialog(context),
    );
  }
}
